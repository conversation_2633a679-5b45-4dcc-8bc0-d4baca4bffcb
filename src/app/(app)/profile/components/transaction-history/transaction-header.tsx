import { RefreshCw } from 'lucide-react';

import { Badge } from '@/components/ui/badge';

interface TransactionHeaderProps {
  onRefresh: () => void;
  loading: boolean;
  refreshing: boolean;
}

export function TransactionHeader({
  onRefresh,
  loading,
  refreshing,
}: TransactionHeaderProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-center">
        <Badge
          variant="outline"
          className="bg-blue-500/10 border-blue-500/30 text-blue-400 text-xs font-medium"
        >
          BETA
        </Badge>
      </div>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-[#f5f5f5]">
          Transaction History
        </h3>
        <button
          onClick={onRefresh}
          disabled={loading || refreshing}
          className="flex items-center gap-2 px-3 py-1.5 text-sm text-[#708499] hover:text-[#f5f5f5] transition-colors disabled:opacity-50"
        >
          <RefreshCw
            className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`}
          />
          Refresh
        </button>
      </div>
    </div>
  );
}
