'use client';

import { Avatar } from '@telegram-apps/telegram-ui';
import { Gift, User } from 'lucide-react';

import { TonLogo } from '@/components/TonLogo';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useRootContext } from '@/root-context';
import { getBalanceInfo } from '@/services/user-service';

export const ProfileUserInfo = () => {
  const { currentUser } = useRootContext();

  if (!currentUser) {
    return null;
  }

  const { balance, availableBalance } = getBalanceInfo(currentUser);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-[#232e3c] flex items-center justify-center">
            {currentUser.photoURL ? (
              <Avatar size={48} src={currentUser.photoURL} />
            ) : (
              <User className="w-8 h-8 text-[#708499]" />
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold">
              {currentUser.displayName || currentUser.name || 'Anonymous User'}
            </h3>
            {currentUser.tg_id && (
              <p className="text-xs text-[#708499]">ID: {currentUser.tg_id}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-[#3a4a5c]">
          <div className="text-center">
            <p className="text-sm text-[#708499]">Available Balance</p>
            <p className="text-xl font-bold text-[#0098EA] flex justify-center">
              {availableBalance.toFixed(2)} <TonLogo size={24} />
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-[#708499]">Locked Balance</p>
            <p className="text-xl font-bold text-[#f5a623] flex justify-center">
              {balance.locked.toFixed(2)} <TonLogo size={24} />
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-[#708499]">Total Balance</p>
            <p className="text-xl font-bold flex justify-center">
              {balance.sum.toFixed(2)} <TonLogo size={24} />
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-[#708499]">My Points</p>
            <p className="text-xl font-bold text-[#10b981] flex justify-center items-center gap-1">
              {currentUser.points ?? 0} <Gift className="w-5 h-5" />
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
