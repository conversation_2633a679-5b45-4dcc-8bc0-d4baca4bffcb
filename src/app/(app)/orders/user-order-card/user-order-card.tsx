'use client';

import { Gift } from 'lucide-react';

import { OrderImage } from '@/components/shared/order-image';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { OrderDeadlineTimer } from '@/components/ui/order/order-deadline-timer';
import { OrderFreezeWarning } from '@/components/ui/order/order-freeze-warning';
import type { OrderEntity, UserType } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useRootContext } from '@/root-context';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

import { UserOrderCardHeader } from './user-order-card-header';
import { UserOrderCardInfo } from './user-order-card-info';

interface UserOrderCardProps {
  order: OrderEntity;
  userType: UserType;
  onClick: () => void;
  onSendGift?: () => void;
}

export function UserOrderCard({
  order,
  userType,
  onClick,
  onSendGift,
}: UserOrderCardProps) {
  const { collections } = useRootContext();
  const collection =
    collections.find((c) => c.id === order.collectionId) || null;
  const { isFreezed } = useOrderTimers({ order, collection });

  const showDeadlineTimer =
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER;

  const showFreezeWarning =
    order.status === OrderStatus.PAID && isFreezed && userType === 'seller';

  const isSecondary = isSecondaryMarketOrder(order);
  console.log('isSecondary', isSecondary);
  // Show send gift button when:
  // - Order has deadline (meaning it's active)
  // - For sellers: status is PAID
  // - For buyers: status is GIFT_SENT_TO_RELAYER
  const showSendGiftButton =
    order.deadline &&
    onSendGift &&
    ((userType === 'seller' && order.status === OrderStatus.PAID) ||
      (userType === 'buyer' &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER));

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on the send gift button
    if ((e.target as HTMLElement).closest('[data-send-gift-button]')) {
      return;
    }
    onClick();
  };

  const handleSendGiftClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSendGift?.();
  };

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={handleCardClick}
    >
      <CardContent className="p-2 flex flex-col h-full">
        <div className="relative mb-1">
          <OrderImage
            order={order}
            collection={collection}
            className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]"
          >
            <UserOrderCardHeader order={order} />
            {isSecondary && (
              <SecondaryMarketBadge className="absolute top-1.5 right-1.5" />
            )}
          </OrderImage>
        </div>

        <UserOrderCardInfo order={order} collection={collection} />

        {showDeadlineTimer && (
          <OrderDeadlineTimer
            {...{
              order,
              collection,
              userType,
            }}
            className={showFreezeWarning ? 'mb-1' : ''}
          />
        )}

        {showFreezeWarning && (
          <OrderFreezeWarning
            order={order}
            userType={userType}
            collection={collection}
            isFreezed={isFreezed}
          />
        )}

        {showSendGiftButton && (
          <Button
            data-send-gift-button
            onClick={handleSendGiftClick}
            className="mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-sm py-2"
          >
            <Gift className="w-4 h-4 mr-2" />
            {order.status === OrderStatus.PAID ? 'Send a Gift' : 'Get a Gift'}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
