'use client';

import { useEffect, useState } from 'react';

import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { GiftInfoDrawer } from '@/components/ui/drawer/gift-info-drawer';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { type OrderEntity, UserType } from '@/constants/core.constants';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useScrollPreservation } from '@/hooks/use-scroll-preservation';
import { useRootContext } from '@/root-context';

import { useUserOrders } from './hooks/use-user-orders';
import { MyOrdersTabs, type MyOrdersTabType } from './my-orders-tabs';
import { UserOrderDetailsDrawer } from './user-order-details-drawer';

export default function OrdersPage() {
  const { currentUser } = useRootContext();
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [showGiftInfoDrawer, setShowGiftInfoDrawer] = useState(false);
  const [giftDrawerMode, setGiftDrawerMode] = useState<'seller' | 'buyer'>(
    'seller',
  );
  const [activeTab, setActiveTab] = useState<MyOrdersTabType>('buy');

  const {
    buyOrdersState,
    sellOrdersState,
    loadOrders,
    loadMoreOrders,
    getUserRole,
  } = useUserOrders(currentUser?.id);

  // Preserve scroll position when drawer opens/closes
  useScrollPreservation({ isOpen: showOrderDetailsDrawer });

  const loadMoreRef = useInfiniteScroll({
    hasMore:
      activeTab === 'buy' ? buyOrdersState.hasMore : sellOrdersState.hasMore,
    loading:
      activeTab === 'buy'
        ? buyOrdersState.loadingMore
        : sellOrdersState.loadingMore,
    onLoadMore: () => loadMoreOrders(activeTab),
  });

  useEffect(() => {
    if (currentUser?.id) {
      loadOrders('buy');
      loadOrders('sell');
    }
  }, [currentUser?.id, loadOrders]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderUpdate = () => {
    if (currentUser?.id) {
      loadOrders('buy');
      loadOrders('sell');
    }
  };

  const handleSendGift = (order: OrderEntity, userType: UserType) => {
    setGiftDrawerMode(userType === UserType.SELLER ? 'seller' : 'buyer');
    setShowGiftInfoDrawer(true);
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <p className="text-[#708499] text-lg">You are not logged in</p>
          <p className="text-[#708499] text-sm">
            Click on login Telegram button to see your orders
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 pb-[75px]">
      <Tabs value={activeTab}>
        <MyOrdersTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          buyOrdersCount={buyOrdersState.orders.length}
          sellOrdersCount={sellOrdersState.orders.length}
        />

        <TabsContent value="buy" className="space-y-4">
          <MarketplaceOrderList
            ref={loadMoreRef}
            variant="user-order"
            orders={buyOrdersState.orders}
            loading={buyOrdersState.loading}
            loadingMore={buyOrdersState.loadingMore}
            hasMore={buyOrdersState.hasMore}
            emptyMessage="No buy orders found"
            onOrderClick={handleOrderClick}
            getUserRole={getUserRole}
            onSendGift={handleSendGift}
          />
        </TabsContent>

        <TabsContent value="sell" className="space-y-4">
          <MarketplaceOrderList
            ref={loadMoreRef}
            variant="user-order"
            orders={sellOrdersState.orders}
            loading={sellOrdersState.loading}
            loadingMore={sellOrdersState.loadingMore}
            hasMore={sellOrdersState.hasMore}
            emptyMessage="No sell orders found"
            onOrderClick={handleOrderClick}
            getUserRole={getUserRole}
            onSendGift={handleSendGift}
          />
        </TabsContent>
      </Tabs>

      <UserOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={selectedOrder ? getUserRole(selectedOrder) : UserType.BUYER}
        onOrderUpdate={handleOrderUpdate}
      />

      <GiftInfoDrawer
        open={showGiftInfoDrawer}
        onOpenChange={setShowGiftInfoDrawer}
        mode={giftDrawerMode}
      />
    </div>
  );
}
