import { defineMessages } from 'react-intl';

export const actionsMessages = defineMessages({
  signIn: {
    id: 'actions.signIn',
    defaultMessage: 'Sign in',
  },
  signUp: {
    id: 'actions.signUp',
    defaultMessage: 'Sign up',
  },
  signOut: {
    id: 'actions.signOut',
    defaultMessage: 'Sign out',
  },

  delete: {
    id: 'actions.delete',
    defaultMessage: 'Delete',
  },
  edit: {
    id: 'actions.edit',
    defaultMessage: 'Edit',
  },
  confirm: {
    id: 'actions.confirm',
    defaultMessage: 'Confirm',
  },
  submit: {
    id: 'actions.submit',
    defaultMessage: 'Submit',
  },
  resend: {
    id: 'actions.resend',
    defaultMessage: 'Resend',
  },
  save: {
    id: 'actions.save',
    defaultMessage: 'Save',
  },
  cancel: {
    id: 'actions.cancel',
    defaultMessage: 'Cancel',
  },
  apply: {
    id: 'actions.apply',
    defaultMessage: 'Apply',
  },
  saved: {
    id: 'actions.saved',
    defaultMessage: 'Saved',
  },
  send: {
    id: 'actions.send',
    defaultMessage: 'Send',
  },
  sent: {
    id: 'actions.sent',
    defaultMessage: 'Sent',
  },
  reject: {
    id: 'actions.reject',
    defaultMessage: 'Reject',
  },
  pause: {
    id: 'actions.pause',
    defaultMessage: 'Pause',
  },
});
