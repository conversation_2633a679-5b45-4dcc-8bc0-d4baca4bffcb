import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { TxType } from "./types";
import { CORS_CONFIG } from "./config";
import { log } from "./utils/logger";
import { addFundsWithHistory } from "./services/balance-service";

const db = admin.firestore();

interface TopUpUserBalanceRequest {
  userId: string;
  amount: number;
}

async function validateAdminUser(uid: string): Promise<void> {
  const userDoc = await db.collection("users").doc(uid).get();
  if (!userDoc.exists || userDoc.data()?.role !== "admin") {
    throw new HttpsError(
      "permission-denied",
      "Only admin users can perform this operation."
    );
  }
}

export const topUpUserBalance = onCall<TopUpUserBalanceRequest>(
  { cors: CORS_CONFIG },
  async (request) => {
    if (!request.auth) {
      throw new HttpsError("unauthenticated", "Authentication required.");
    }

    const { userId, amount } = request.data;

    if (!userId) {
      throw new HttpsError("invalid-argument", "User ID is required.");
    }

    if (!amount || amount <= 0) {
      throw new HttpsError(
        "invalid-argument",
        "Amount must be a positive number."
      );
    }

    await validateAdminUser(request.auth.uid);

    try {
      // Check if target user exists
      const targetUserDoc = await db.collection("users").doc(userId).get();
      if (!targetUserDoc.exists) {
        throw new HttpsError("not-found", "Target user not found.");
      }

      const targetUserData = targetUserDoc.data();
      const targetUserName = targetUserData?.displayName || targetUserData?.telegram_handle || "Unknown User";

      // Add funds with transaction history
      const result = await addFundsWithHistory({
        userId,
        amount,
        txType: TxType.DEPOSIT,
        description: `Admin top-up by ${request.auth.uid}`,
      });

      log.info("Admin topped up user balance", {
        adminUserId: request.auth.uid,
        targetUserId: userId,
        targetUserName,
        amount,
        operation: "admin_top_up_user_balance",
        newBalance: result.newBalance,
      });

      return {
        success: true,
        message: `Successfully added ${amount} TON to ${targetUserName}'s balance`,
        newBalance: result.newBalance,
      };
    } catch (error) {
      log.error("Error in topUpUserBalance function", error, {
        adminUserId: request.auth.uid,
        targetUserId: userId,
        amount,
        operation: "admin_top_up_user_balance",
      });

      if (error instanceof HttpsError) {
        throw error;
      }

      throw new HttpsError(
        "internal",
        (error as any).message ?? "Server error while topping up user balance."
      );
    }
  }
);
